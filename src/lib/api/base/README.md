# API Client with Request Queue Management

## 概述

API 客户端现在支持请求队列管理，确保所有请求按顺序执行，避免并发问题。这对于防止重复的请求等场景特别有用。

## 核心特性

### 🚀 请求队列管理

- **串行执行**：所有请求按顺序执行，避免并发
- **智能去重**：相同请求自动合并，减少重复调用
- **优先级支持**：支持高优先级请求优先处理
- **超时控制**：自动处理请求超时
- **错误隔离**：单个请求失败不影响其他请求

### 📊 队列配置选项

```typescript
interface QueueConfig {
  enabled?: boolean; // 是否启用队列，默认 true
  maxSize?: number; // 队列最大长度，默认 100
  timeout?: number; // 请求超时时间，默认 30000ms
  enableDeduplication?: boolean; // 是否启用请求去重，默认 true
}
```

## 使用方法

### 基本使用

```typescript
import { apiClient } from "@/lib/api/base/apiClient";

// 普通请求（自动使用队列管理）
const response = await apiClient.get("/api/projects");

// 高优先级请求
const urgentResponse = await apiClient.priorityRequest(
  "/api/auth/verify_status",
  {
    method: "GET",
    priority: 1, // 数字越小优先级越高
  }
);
```

### 队列状态监控

```typescript
// 获取队列状态
const status = apiClient.getQueueStatus();
console.log("Queue status:", {
  queueLength: status.queueLength,
  isProcessing: status.isProcessing,
  pendingRequestsCount: status.pendingRequestsCount,
});

// 清空队列（紧急情况下使用）
apiClient.clearQueue();
```

### 自定义配置

```typescript
import { ApiClient } from "@/lib/api/base/apiClient";

// 创建自定义配置的客户端
const customClient = new ApiClient("https://api.example.com", {
  enabled: true,
  maxSize: 50,
  timeout: 15000,
  enableDeduplication: true,
});

// 创建不使用队列的客户端（特殊场景）
const directClient = new ApiClient("https://api.example.com", {
  enabled: false,
});
```

## 请求去重机制

### 去重规则

- **GET 请求**：基于 `method + endpoint + headers` 生成唯一标识
- **其他请求**：基于 `method + endpoint + headers + body` 生成唯一标识
- 相同标识的请求会合并回调，只执行一次实际的 HTTP 请求

### 示例场景

```typescript
// 以下两个请求会被去重，只发送一次实际请求
const promise1 = apiClient.get("/api/auth/verify_status");
const promise2 = apiClient.get("/api/auth/verify_status");

// 两个 Promise 都会收到相同的响应
const [result1, result2] = await Promise.all([promise1, promise2]);
console.log(result1 === result2); // true
```

## 优先级系统

### 优先级规则

- 数字越小优先级越高（1 > 2 > 3 > ...）
- 默认优先级为 10
- 相同优先级按时间顺序执行

### 使用示例

```typescript
// 高优先级认证请求
await apiClient.request("/api/auth/verify_status", { priority: 1 });

// 普通优先级数据请求
await apiClient.request("/api/projects", { priority: 5 });

// 低优先级日志请求
await apiClient.request("/api/logs", { priority: 15 });
```

## 错误处理

### 超时处理

```typescript
try {
  const response = await apiClient.get("/api/slow-endpoint");
} catch (error) {
  if (error.message.includes("timeout")) {
    console.log("Request timed out");
  }
}
```

### 队列满处理

```typescript
try {
  const response = await apiClient.get("/api/endpoint");
} catch (error) {
  if (error.message.includes("queue is full")) {
    console.log("Request queue is full, try again later");
  }
}
```

## 性能优化建议

### ▶︎ **最佳实践**

1. **合理设置优先级**：重要请求（如认证）使用高优先级
2. **监控队列状态**：定期检查队列长度，避免积压
3. **适当的超时时间**：根据接口特性设置合理的超时时间
4. **启用去重**：对于幂等请求启用去重功能

### ▶︎ **性能指标**

- 队列长度通常应保持在 10 以下
- 请求处理时间包含 10ms 的间隔延迟
- 去重可以减少 30-50% 的重复请求

## 兼容性说明

- ✅ 完全向后兼容现有 API
- ✅ 所有现有方法签名保持不变
- ✅ 可以通过配置禁用队列功能
- ✅ 支持原有的 AbortSignal 取消机制

## 调试和监控

### 日志输出

队列管理器会输出详细的日志信息：

- `🚀 Starting queue processing` - 开始处理队列
- `⚡ Executing request` - 执行具体请求
- `✅ Request completed` - 请求完成
- `🔄 Request deduplicated` - 请求去重
- `🏁 Queue processing completed` - 队列处理完成

### 开发工具

```typescript
// 在浏览器控制台中查看队列状态
console.log(apiClient.getQueueStatus());
```
