/**
 * 文档服务
 * 基于 API 文档的文档端点实现
 */

import { apiClient } from "../base/apiClient";
import { handleApiError } from "../base/errorHandler";
import {
  Doc,
  DocCreateRequest,
  DocUpdateRequest,
  DocDeleteResponse,
  UUID,
} from "../types";
import logger from "@/utils/logger";

// 创建文档服务专用的 logger
const docLogger = logger.createPrefixed("DOC");

export class DocService {
  /**
   * 根据ID获取文档
   * GET /docs/{doc_id}
   */
  async getDoc(docId: UUID): Promise<Doc> {
    try {
      docLogger.log("📄 开始获取文档:", { 文档ID: docId });
      const response = await apiClient.get<Doc>(`/docs/${docId}`);
      docLogger.log("✅ 文档获取成功:", {
        文档ID: docId,
        文档名称: response.data.doc_name,
      });
      return response.data;
    } catch (error) {
      docLogger.error("❌ 获取文档失败:", { 文档ID: docId, 错误: error });
      throw handleApiError(error);
    }
  }

  /**
   * 创建文档
   * POST /docs/
   */
  async createDoc(request: DocCreateRequest): Promise<Doc> {
    try {
      docLogger.log("📝 开始创建文档:", {
        项目ID: request.project_id,
        文档名称: request.doc_name,
      });
      const response = await apiClient.post<Doc>("/docs", request);
      docLogger.log("✅ 文档创建成功:", {
        文档ID: response.data.id,
        文档名称: response.data.doc_name,
      });
      return response.data;
    } catch (error) {
      docLogger.error("❌ 创建文档失败:", { 请求数据: request, 错误: error });
      throw handleApiError(error);
    }
  }

  /**
   * 更新文档（部分更新）
   * PATCH /docs/{doc_id}
   */
  async updateDoc(docId: UUID, request: DocUpdateRequest): Promise<Doc> {
    try {
      docLogger.log("✏️ 开始更新文档:", {
        文档ID: docId,
        更新数据: request,
      });
      const response = await apiClient.patch<Doc>(`/docs/${docId}`, request);
      docLogger.log("✅ 文档更新成功:", {
        文档ID: docId,
        文档名称: response.data.doc_name,
      });
      return response.data;
    } catch (error) {
      docLogger.error("❌ 更新文档失败:", {
        文档ID: docId,
        请求数据: request,
        错误: error,
      });
      throw handleApiError(error);
    }
  }

  /**
   * 删除文档（软删除）
   * DELETE /docs/{doc_id}
   */
  async deleteDoc(docId: UUID): Promise<DocDeleteResponse> {
    try {
      docLogger.log("🗑️ 开始删除文档:", { 文档ID: docId });
      const response = await apiClient.delete<DocDeleteResponse>(
        `/docs/${docId}`
      );
      docLogger.log("✅ 文档删除成功:", {
        文档ID: docId,
        响应: response.data,
      });
      return response.data;
    } catch (error) {
      docLogger.error("❌ 删除文档失败:", { 文档ID: docId, 错误: error });
      throw handleApiError(error);
    }
  }
}

// 导出服务实例
export const docService = new DocService();
