/**
 * 项目服务模块
 * 处理项目相关的 API 请求
 */

import { apiClient } from "../base/apiClient";
import { handleApiError } from "../base/errorHandler";
import {
  Project,
  ProjectItem,
  ProjectCreateRequest,
  ProjectUpdateRequest,
  ProjectTree,
} from "../types";
import logger from "@/utils/logger";

// 创建项目服务专用的 logger
const projectLogger = logger.createPrefixed("PROJECT");

/**
 * 项目服务类
 */
export class ProjectService {
  /**
   * 获取用户的所有项目列表
   * GET /projects/
   */
  async getProjects(): Promise<ProjectItem[]> {
    try {
      projectLogger.log("🔄 开始获取项目列表...");
      const response = await apiClient.get<ProjectItem[]>("/projects");
      projectLogger.log("✅ 项目列表获取成功:", {
        项目数量: response.data.length,
        响应状态: response.status,
        数据大小估算: JSON.stringify(response.data).length + " 字符",
        项目列表: response.data,
      });
      return response.data;
    } catch (error) {
      projectLogger.error("❌ 获取项目列表失败:", error);
      throw handleApiError(error);
    }
  }

  /**
   * 创建项目
   * POST /projects/
   */
  async createProject(data: ProjectCreateRequest): Promise<Project> {
    try {
      projectLogger.log("🆕 开始创建项目:", data);
      const response = await apiClient.post<Project>("/projects", data);
      projectLogger.log("✅ 项目创建成功:", {
        响应状态: response.status,
        数据大小估算: JSON.stringify(response.data).length + " 字符",
        创建的项目: response.data,
      });
      return response.data;
    } catch (error) {
      projectLogger.error("❌ 创建项目失败:", error);
      throw handleApiError(error);
    }
  }

  /**
   * 更新项目
   * PATCH /projects/{project_id}
   */
  async updateProject(
    projectId: string,
    data: ProjectUpdateRequest
  ): Promise<Project> {
    try {
      const response = await apiClient.patch<Project>(
        `/projects/${projectId}`,
        data
      );
      return response.data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 删除项目
   * DELETE /projects/{project_id}
   */
  async deleteProject(projectId: string): Promise<void> {
    try {
      await apiClient.delete(`/projects/${projectId}`);
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * 获取项目文件树
   * GET /projects/{project_id}/tree
   */
  async getProjectTree(projectId: string): Promise<ProjectTree[]> {
    try {
      projectLogger.log("🌳 开始获取项目文件树:", { 项目ID: projectId });
      const response = await apiClient.get<ProjectTree[]>(
        `/projects/${projectId}/tree`
      );
      projectLogger.log("✅ 项目文件树获取成功:", {
        项目ID: projectId,
        文件数量: response.data.length,
        文件列表: response.data,
      });
      return response.data;
    } catch (error) {
      projectLogger.error("❌ 获取项目文件树失败:", {
        项目ID: projectId,
        错误: error,
      });
      throw handleApiError(error);
    }
  }
}

// 导出项目服务实例
export const projectService = new ProjectService();
