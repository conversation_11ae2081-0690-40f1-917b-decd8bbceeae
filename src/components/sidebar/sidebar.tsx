import React, { useState, useCallback } from "react";
import TabSwitcher from "./components/tabswitcher";
import TabContent from "./components/tabcontent";
import Inbox from "./components/inbox";
import ErrorState from "@/components/ui/status/ErrorState";
import LoadingState from "@/components/ui/status/LoadingState";

import SidebarContextMenu from "./components/SidebarContextMenu";
import { useProjectFiles } from "@/hooks/editor/data/useProjectFiles";
import { useSidebarContextMenu } from "./hooks/useSidebarContextMenu";
import { FileItem } from "./components/filelist";
import FileOperationModal, {
  OperationType,
} from "./components/FileOperationModal";
import {
  createVirtualFolder,
  cleanupVirtualFoldersOnFileCreate,
  createVirtualFoldersOnFileDelete,
  deleteVirtualFolder,
  collectVirtualFoldersInFolder,
} from "../../utils/virtualFolders";

import { docService } from "../../lib/api/services/docService";
import { Region } from "../../lib/api/types/common";
import { showSuccessToast, showErrorToast } from "../../utils/toast";

// 导入Tab接口
import type { Tab } from "./components/tabswitcher";

interface SidebarProps {
  className?: string;
  onFilePathChange?: (filePath: string) => void;
  projectId?: string; // 项目ID，用于加载项目相关的文件
}

const Sidebar: React.FC<SidebarProps> = ({
  className = "",
  onFilePathChange,
  projectId,
}) => {
  const [selectedFilePath, setSelectedFilePath] = useState<
    string | undefined
  >();
  const [activeTabId, setActiveTabId] = useState("personal");

  // 对话框状态
  const [dialogState, setDialogState] = useState<{
    operation: OperationType | null;
    isOpen: boolean;
    parentPath?: string;
    targetFile?: FileItem;
    deleteInfo?: {
      isFolder: boolean;
      fileCount?: number;
      folderCount?: number;
    };
  }>({
    operation: null,
    isOpen: false,
  });

  // 强制重新渲染文件列表的状态
  const [fileListKey, setFileListKey] = useState(0);

  // 递归收集文件夹中的所有文件
  const collectAllFilesInFolder = (folderItem: FileItem): FileItem[] => {
    const allFiles: FileItem[] = [];

    if (!folderItem.children) {
      console.log(
        `📂 No children in folder: ${folderItem.name} (${folderItem.path})`
      );
      return allFiles;
    }

    console.log(
      `📂 Collecting files from folder: ${folderItem.name} (${folderItem.path}), children: ${folderItem.children.length}`
    );

    folderItem.children.forEach((child) => {
      console.log(
        `  🔍 Processing child: ${child.name} (${child.path}) [${child.type}]`
      );
      if (child.type === "file") {
        allFiles.push(child);
        console.log(`    ✅ Added file: ${child.name}`);
      } else if (child.type === "folder") {
        // 递归收集子文件夹中的文件
        const subFiles = collectAllFilesInFolder(child);
        allFiles.push(...subFiles);
        console.log(
          `    📁 Added ${subFiles.length} files from subfolder: ${child.name}`
        );
      }
    });

    console.log(
      `📊 Total files collected from ${folderItem.name}: ${allFiles.length}`
    );
    return allFiles;
  };

  // 递归收集文件夹中的所有子文件夹
  const collectAllSubFolders = (folderItem: FileItem): FileItem[] => {
    const allFolders: FileItem[] = [];

    if (!folderItem.children) return allFolders;

    folderItem.children.forEach((child) => {
      if (child.type === "folder") {
        allFolders.push(child);
        // 递归收集子文件夹
        allFolders.push(...collectAllSubFolders(child));
      }
    });

    return allFolders;
  };

  // 在文件树中查找指定路径的文件夹
  const findFolderInTree = (
    items: FileItem[],
    targetPath: string
  ): FileItem | null => {
    for (const item of items) {
      if (item.path === targetPath) {
        return item;
      }
      if (item.children) {
        const found = findFolderInTree(item.children, targetPath);
        if (found) return found;
      }
    }
    return null;
  };

  // 获取文件树中的所有路径（用于调试）
  const getAllPathsInTree = (items: FileItem[]): string[] => {
    const paths: string[] = [];
    items.forEach((item) => {
      if (item.path) paths.push(item.path);
      if (item.children) {
        paths.push(...getAllPathsInTree(item.children));
      }
    });
    return paths;
  };

  // 分析删除目标的内容 - 直接使用传入的 target
  const analyzeDeleteTarget = (target: FileItem) => {
    console.log("🎯 analyzeDeleteTarget called with:", target);

    if (!target || target.type !== "folder") {
      return { isFolder: false };
    }

    // 直接使用传入的 target，它应该就是文件树中的正确实例
    console.log("📊 Target folder children:", target.children?.length || 0);
    console.log(
      "� Target folder children details:",
      target.children?.map((c) => ({
        name: c.name,
        path: c.path,
        type: c.type,
        id: c.id,
      }))
    );

    // 先打印完整的文件夹结构
    const printFolderStructure = (folder: FileItem, indent = 0): void => {
      const prefix = "  ".repeat(indent);
      console.log(
        `${prefix}📁 ${folder.name} (${folder.path}) [${folder.type}] ${
          folder.isVirtual ? "[VIRTUAL]" : "[REAL]"
        }`
      );
      if (folder.children) {
        folder.children.forEach((child) => {
          if (child.type === "file") {
            console.log(
              `${prefix}  📄 ${child.name} (${child.path}) [${child.type}] ID: ${child.id}`
            );
          } else {
            printFolderStructure(child, indent + 1);
          }
        });
      }
    };

    console.log("🌳 COMPLETE FOLDER STRUCTURE:");
    printFolderStructure(target);

    // 收集文件夹中的所有文件和子文件夹
    const allFiles = collectAllFilesInFolder(target);
    const allSubFolders = collectAllSubFolders(target);

    console.log("🔍 TREE-BASED DELETE ANALYSIS:", {
      "=== TARGET INFO ===": {
        targetPath: target.path,
        targetName: target.name,
        isVirtual: target.isVirtual,
      },
      "=== FOLDER CONTENTS ===": {
        directChildren: target.children?.length || 0,
        allFiles: allFiles.length,
        allSubFolders: allSubFolders.length,
      },
      "=== FILE DETAILS ===": allFiles.map((f) => ({
        name: f.name,
        path: f.path,
        id: f.id,
      })),
      "=== SUBFOLDER DETAILS ===": allSubFolders.map((f) => ({
        name: f.name,
        path: f.path,
        isVirtual: f.isVirtual,
      })),
    });

    return {
      isFolder: true,
      fileCount: allFiles.length,
      folderCount: allSubFolders.length,
    };
  };

  // 定义标签
  const tabs: Tab[] = [
    { id: "personal", icon: "Personal", label: "Personal" },
    { id: "team", icon: "TeamShared", label: "Team Shared" },
    { id: "laws", icon: "LawsAndPrec", label: "Laws & Prec." },
  ];

  // 获取当前活动tab的标签名
  const getActiveTabLabel = () => {
    const activeTab = tabs.find((tab) => tab.id === activeTabId);
    return activeTab?.label || "Personal";
  };

  // 根据当前活动tab获取对应的Region
  const getCurrentRegion = (): Region => {
    console.log("🎯 getCurrentRegion called with activeTabId:", activeTabId);
    switch (activeTabId) {
      case "personal":
        return Region.PERSONAL;
      case "team":
        return Region.SHARED;
      case "laws":
        return Region.REFERENCE;
      default:
        console.warn(
          "⚠️ Unknown activeTabId, defaulting to PERSONAL:",
          activeTabId
        );
        return Region.PERSONAL;
    }
  };

  // 使用 useProjectFiles Hook 获取真实的项目文件数据
  // 只有在有有效项目ID时才获取文件数据
  const { files, isLoading, error, refetch } = useProjectFiles(projectId || "");

  // 调试：检查文件数据加载状态
  React.useEffect(() => {
    console.log("📊 Files data status:", {
      projectId,
      isLoading,
      error: error,
      filesCount: files?.length || 0,
      files: files?.slice(0, 5), // 显示前5个文件
      hasProjectId: !!projectId,
      hookCalled: true,
    });
  }, [projectId, isLoading, error, files]);

  // 额外检查：在删除分析时也检查文件数据
  React.useEffect(() => {
    if (files && files.length > 0) {
      console.log(
        "📁 Current files in project:",
        files.map((f) => ({
          name: f.name,
          id: f.id,
          region: f.region,
        }))
      );
    }
  }, [files]);

  const handleTabChange = (tabId: string) => {
    console.log("🔄 Tab changed to:", tabId);
    setActiveTabId(tabId);
  };

  // 统一的右键菜单管理
  const {
    isOpen: isContextMenuOpen,
    menuData,
    closeMenu,
    handleSidebarContextMenu,
    handleFileContextMenu,
  } = useSidebarContextMenu(projectId);

  const handleFileSelect = (file: any) => {
    setSelectedFilePath(file.path);
    // 通知父组件文件路径变化
    if (onFilePathChange) {
      onFilePathChange(file.path);
    }
    console.log("Selected file:", file);
    // In a real app, you would open the file in the editor
  };

  // 处理右键菜单操作
  const handleContextMenuAction = useCallback(
    (actionId: string, data?: any) => {
      // console.log("🖱️ Context menu action:", actionId, data);
      // console.log("🏷️ Current activeTabId when action triggered:", activeTabId);

      switch (actionId) {
        case "new-folder":
          setDialogState({
            operation: "create-folder",
            isOpen: true,
            parentPath: undefined,
          });
          break;
        case "new-file":
          setDialogState({
            operation: "create-file",
            isOpen: true,
            parentPath: undefined,
          });
          break;
        case "import-files":
          // TODO: 实现导入文件功能
          console.log("Import files");
          break;
        case "project-info":
          // TODO: 实现显示项目信息功能
          console.log("Show project info");
          break;
        case "rename":
          // TODO: 实现重命名功能
          console.log("Rename:", data?.path);
          break;
        case "duplicate":
          // TODO: 实现复制功能
          console.log("Duplicate:", data?.path);
          break;

        case "add-to-chat":
          // TODO: 实现添加到对话功能
          console.log("Add to chat:", data?.path);
          break;
        case "new-file-in-folder":
          setDialogState({
            operation: "create-file",
            isOpen: true,
            parentPath: data?.path,
          });
          break;
        case "new-subfolder":
          setDialogState({
            operation: "create-folder",
            isOpen: true,
            parentPath: data?.path,
          });
          break;
        case "delete":
          // 分析删除目标的内容
          const deleteInfo = analyzeDeleteTarget(data);
          setDialogState({
            operation: "delete",
            isOpen: true,
            targetFile: data,
            deleteInfo,
          });
          break;
        default:
          console.warn("❌ Unknown action - no case matched:", actionId);
      }
    },
    [activeTabId]
  );

  // 处理新建文件夹
  const handleCreateFolder = useCallback(
    async (folderName: string) => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      try {
        createVirtualFolder(
          projectId,
          folderName,
          getCurrentRegion(),
          dialogState.parentPath
        );
        showSuccessToast(`Folder "${folderName}" created successfully`);
        // 强制重新渲染文件列表以显示虚拟文件夹
        setFileListKey((prev) => prev + 1);
      } catch (error) {
        const message =
          error instanceof Error ? error.message : "Failed to create folder";
        showErrorToast(message);
        throw error;
      }
    },
    [projectId, dialogState.parentPath, refetch, activeTabId]
  );

  // 处理新建文件
  const handleCreateFile = useCallback(
    async (fileName: string) => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      try {
        // 构建完整文件路径
        const fullPath = dialogState.parentPath
          ? `${dialogState.parentPath}/${fileName}`
              .replace(/\/+/g, "/")
              .replace(/^\//, "")
          : fileName;

        const currentRegion = getCurrentRegion();
        console.log("🏷️ Current tab info:", { activeTabId, currentRegion });
        console.log(`📝 Creating document in region:`, currentRegion);

        // 所有文件都通过 docService.createDoc 创建
        const result = await docService.createDoc({
          project_id: projectId,
          region: currentRegion,
          doc_name: fullPath,
          content: "",
        });
        console.log("� Document created:", result);

        // 清理可能存在的虚拟文件夹
        cleanupVirtualFoldersOnFileCreate(projectId, fullPath);

        showSuccessToast(`File "${fileName}" created successfully`);
        // 刷新文件列表并强制重新渲染
        await refetch();
        setFileListKey((prev) => prev + 1);
      } catch (error) {
        const message =
          error instanceof Error ? error.message : "Failed to create file";
        showErrorToast(message);
        throw error;
      }
    },
    [projectId, dialogState.parentPath, refetch, activeTabId]
  );

  // 处理删除操作
  const handleDelete = useCallback(async () => {
    // console.log("🗑️ handleDelete called with:", dialogState.targetFile);
    if (!dialogState.targetFile) return;

    const file = dialogState.targetFile;
    const currentRegion = getCurrentRegion();

    try {
      if (file.type === "folder") {
        // 删除文件夹（可能包含文件）
        await handleFolderDelete(file, currentRegion);
      } else {
        // 删除单个文件
        await docService.deleteDoc(file.id!);

        // 创建虚拟文件夹以保留文件夹结构
        if (file.path && file.path.includes("/")) {
          createVirtualFoldersOnFileDelete(
            projectId!,
            file.path,
            currentRegion
          );
        }

        showSuccessToast(`File "${file.name}" deleted successfully`);
        await refetch();
      }

      setFileListKey((prev) => prev + 1);
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "Failed to delete item";
      showErrorToast(message);
      throw error;
    }
  }, [dialogState.targetFile, projectId, getCurrentRegion, refetch]);

  // 处理文件夹删除（递归删除所有内容）
  const handleFolderDelete = useCallback(
    async (folder: FileItem, currentRegion: Region) => {
      console.log("🗑️ handleFolderDelete called with:", folder);

      // 使用相同的逻辑收集文件夹内容
      const allFiles = collectAllFilesInFolder(folder);
      const allSubFolders = collectAllSubFolders(folder);

      // 收集所有虚拟文件夹（用户创建的）
      const userVirtualFolders = collectVirtualFoldersInFolder(
        projectId!,
        folder.path!,
        currentRegion
      );

      let deletedCount = 0;
      // 计算总项目数：只计算文件夹内的内容（文件 + 用户虚拟文件夹），不包括文件夹本身
      const totalItems = allFiles.length + userVirtualFolders.length - 1;

      console.log("🗑️ Deleting folder contents:", {
        folderPath: folder.path,
        allFiles: allFiles.length,
        allSubFolders: allSubFolders.length,
        userVirtualFolders: userVirtualFolders.length,
        totalItems,
        fileDetails: allFiles.map((f) => ({
          name: f.name,
          id: f.id,
          path: f.path,
        })),
        virtualFolderDetails: userVirtualFolders.map((f) => ({
          name: f.name,
          path: f.path,
        })),
      });

      try {
        // 删除所有真实文件
        if (allFiles.length > 0) {
          console.log(`🗑️ Deleting ${allFiles.length} files...`);
          const deletePromises = allFiles.map(async (file) => {
            console.log(`🗑️ Deleting file: ${file.name} (${file.id})`);
            await docService.deleteDoc(file.id!);
            deletedCount++;
            return file;
          });

          await Promise.all(deletePromises);
          console.log(`✅ Deleted ${allFiles.length} files`);
        }

        // 删除所有用户创建的虚拟文件夹
        if (userVirtualFolders.length > 0) {
          console.log(
            `🗑️ Deleting ${userVirtualFolders.length} virtual folders...`
          );
          userVirtualFolders.forEach((virtualFolder) => {
            console.log(`🗑️ Deleting virtual folder: ${virtualFolder.path}`);
            deleteVirtualFolder(projectId!, virtualFolder.path);
            deletedCount++;
          });
          console.log(
            `✅ Deleted ${userVirtualFolders.length} virtual folders`
          );
        }

        // 如果目标文件夹本身是虚拟的，也要删除它
        if (folder.isVirtual) {
          console.log(`🗑️ Deleting target virtual folder: ${folder.path}`);
          deleteVirtualFolder(projectId!, folder.path!);
          deletedCount++;
        }

        // 刷新文件列表
        await refetch();

        if (totalItems > 0) {
          showSuccessToast(
            `Folder "${folder.name}" and ${totalItems} item${
              totalItems > 1 ? "s" : ""
            } deleted successfully`
          );
        } else {
          showSuccessToast(`Folder "${folder.name}" deleted successfully`);
        }
      } catch (error) {
        showErrorToast(
          `Failed to delete folder. ${deletedCount} of ${totalItems} items were deleted.`
        );
        throw error;
      }
    },
    [projectId, files, refetch]
  );

  // 处理对话框确认
  const handleDialogConfirm = useCallback(
    async (value?: string) => {
      switch (dialogState.operation) {
        case "create-folder":
          if (value) await handleCreateFolder(value);
          break;
        case "create-file":
          if (value) await handleCreateFile(value);
          break;
        case "delete":
          await handleDelete();
          break;
      }
    },
    [dialogState.operation, handleCreateFolder, handleCreateFile, handleDelete]
  );

  // 关闭对话框
  const closeDialog = useCallback(() => {
    setDialogState({ operation: null, isOpen: false });
  }, []);

  return (
    <div className={`sidebar h-full pt-4 flex flex-col ${className}`}>
      <TabSwitcher tabs={tabs} onTabChange={handleTabChange} />
      <div
        className="flex-1 overflow-hidden"
        onContextMenu={handleSidebarContextMenu}
      >
        {isLoading ? (
          <LoadingState
            text="Loading files..."
            className="h-100 px-4 flex items-center justify-center"
          />
        ) : error ? (
          <ErrorState
            title="Failed to Load Files"
            message="Please check your network."
            retryText="Retry"
            onRetry={refetch}
            className="h-100 px-4 flex items-center justify-center"
          />
        ) : (
          <TabContent
            key={fileListKey}
            activeTabId={activeTabId}
            files={files}
            onFileSelect={handleFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={handleFileContextMenu}
            projectId={projectId}
            onSidebarContextMenu={handleSidebarContextMenu}
          />
        )}
      </div>

      {/* 使用抽离出来的 Inbox 组件 */}
      <Inbox
        onFileSelect={handleFileSelect}
        selectedFilePath={selectedFilePath}
        activeTab={getActiveTabLabel()}
      />

      {/* 统一的右键菜单 */}
      <SidebarContextMenu
        isOpen={isContextMenuOpen}
        setIsOpen={closeMenu}
        menuData={menuData}
        onAction={handleContextMenuAction}
      />

      {/* 文件/文件夹操作对话框 */}
      <FileOperationModal
        isOpen={dialogState.isOpen}
        onClose={closeDialog}
        operation={dialogState.operation!}
        onConfirm={handleDialogConfirm}
        targetName={dialogState.targetFile?.name}
        parentPath={dialogState.parentPath}
        deleteInfo={dialogState.deleteInfo}
      />
    </div>
  );
};

export default Sidebar;
