/**
 * 项目信息 Hook
 * 根据项目ID获取单个项目的详细信息
 */

import { useMemo } from "react";
import { useProjectsData } from "./useProjectsData";
import { ProjectItem } from "@/lib/api/types/project";

/**
 * Hook 返回值接口
 */
export interface UseProjectInfoReturn {
  project: ProjectItem | null;
  isLoading: boolean;
  error: string | null;
  projectName: string;
  projectDescription: string;
}

/**
 * 项目信息 Hook
 * 
 * ▶︎ **核心特性**:
 * - 从项目列表中根据ID查找特定项目
 * - 自动处理加载状态和错误状态
 * - 提供便捷的项目名称和描述访问
 * - 基于现有的项目数据缓存，无需额外API调用
 * 
 * ▶︎ **使用场景**:
 * - 编辑器页面显示当前项目信息
 * - 项目详情页面
 * - 需要单个项目信息的任何组件
 * 
 * ▶︎ **性能优化**:
 * - 使用 useMemo 缓存查找结果
 * - 复用现有的项目数据，避免重复请求
 * 
 * @param projectId 项目ID
 * @returns 项目信息和状态
 */
export const useProjectInfo = (projectId: string | undefined): UseProjectInfoReturn => {
  // 获取所有项目数据
  const { projects, isLoading, error } = useProjectsData({
    autoFetch: true,
    cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
  });

  // 根据ID查找项目
  const project = useMemo(() => {
    if (!projectId || !projects.length) {
      return null;
    }
    
    return projects.find(p => p.id === projectId) || null;
  }, [projectId, projects]);

  // 便捷的访问器
  const projectName = project?.name || "";
  const projectDescription = project?.description || "";

  return {
    project,
    isLoading,
    error,
    projectName,
    projectDescription,
  };
};
